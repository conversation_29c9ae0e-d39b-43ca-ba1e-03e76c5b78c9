﻿namespace EMS.Gui.StoreGui
{
    partial class AddMovementToTheVaultForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            groupBox2 = new GroupBox();
            label5 = new Label();
            dateTimePicker = new DateTimePicker();
            label4 = new Label();
            textBoxValue = new TextBox();
            label3 = new Label();
            radioButtonAddMoneyToStore = new RadioButton();
            radioButtonWithdrawMoneyFromStore = new RadioButton();
            richTextBoxNote = new RichTextBox();
            label1 = new Label();
            buttonSave = new Button();
            groupBox2.SuspendLayout();
            SuspendLayout();
            // 
            // groupBox2
            // 
            groupBox2.AutoSize = true;
            groupBox2.Controls.Add(label1);
            groupBox2.Controls.Add(richTextBoxNote);
            groupBox2.Controls.Add(radioButtonWithdrawMoneyFromStore);
            groupBox2.Controls.Add(radioButtonAddMoneyToStore);
            groupBox2.Controls.Add(label5);
            groupBox2.Controls.Add(dateTimePicker);
            groupBox2.Controls.Add(label4);
            groupBox2.Controls.Add(textBoxValue);
            groupBox2.Controls.Add(label3);
            groupBox2.Font = new Font("Cairo", 9.857142F, FontStyle.Regular, GraphicsUnit.Point, 0);
            groupBox2.Location = new Point(12, 12);
            groupBox2.Name = "groupBox2";
            groupBox2.Size = new Size(516, 425);
            groupBox2.TabIndex = 3;
            groupBox2.TabStop = false;
            groupBox2.Text = "بيانات الحركة";
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Location = new Point(408, 330);
            label5.Name = "label5";
            label5.Size = new Size(83, 43);
            label5.TabIndex = 32;
            label5.Text = "التاريخ :";
            // 
            // dateTimePicker
            // 
            dateTimePicker.Format = DateTimePickerFormat.Short;
            dateTimePicker.Location = new Point(16, 324);
            dateTimePicker.Name = "dateTimePicker";
            dateTimePicker.Size = new Size(351, 51);
            dateTimePicker.TabIndex = 31;
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.ForeColor = Color.Red;
            label4.Location = new Point(373, 128);
            label4.Name = "label4";
            label4.Size = new Size(29, 43);
            label4.TabIndex = 10;
            label4.Text = "*";
            // 
            // textBoxValue
            // 
            textBoxValue.Font = new Font("Cairo", 9.857142F);
            textBoxValue.Location = new Point(16, 125);
            textBoxValue.Margin = new Padding(3, 8, 3, 3);
            textBoxValue.Name = "textBoxValue";
            textBoxValue.RightToLeft = RightToLeft.No;
            textBoxValue.Size = new Size(351, 51);
            textBoxValue.TabIndex = 2;
            textBoxValue.TextAlign = HorizontalAlignment.Center;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new Point(408, 128);
            label3.Name = "label3";
            label3.Size = new Size(92, 43);
            label3.TabIndex = 8;
            label3.Text = "القيمة :";
            // 
            // radioButtonAddMoneyToStore
            // 
            radioButtonAddMoneyToStore.AutoSize = true;
            radioButtonAddMoneyToStore.Checked = true;
            radioButtonAddMoneyToStore.ForeColor = Color.Green;
            radioButtonAddMoneyToStore.Location = new Point(281, 60);
            radioButtonAddMoneyToStore.Name = "radioButtonAddMoneyToStore";
            radioButtonAddMoneyToStore.Size = new Size(219, 47);
            radioButtonAddMoneyToStore.TabIndex = 33;
            radioButtonAddMoneyToStore.TabStop = true;
            radioButtonAddMoneyToStore.Text = "اضافة مبلغ للخزينة";
            radioButtonAddMoneyToStore.UseVisualStyleBackColor = true;
            // 
            // radioButtonWithdrawMoneyFromStore
            // 
            radioButtonWithdrawMoneyFromStore.AutoSize = true;
            radioButtonWithdrawMoneyFromStore.ForeColor = Color.Red;
            radioButtonWithdrawMoneyFromStore.Location = new Point(16, 60);
            radioButtonWithdrawMoneyFromStore.Name = "radioButtonWithdrawMoneyFromStore";
            radioButtonWithdrawMoneyFromStore.Size = new Size(243, 47);
            radioButtonWithdrawMoneyFromStore.TabIndex = 34;
            radioButtonWithdrawMoneyFromStore.Text = "خصم مبلغ من الخزينة";
            radioButtonWithdrawMoneyFromStore.UseVisualStyleBackColor = true;
            // 
            // richTextBoxNote
            // 
            richTextBoxNote.Location = new Point(16, 182);
            richTextBoxNote.Name = "richTextBoxNote";
            richTextBoxNote.Size = new Size(351, 120);
            richTextBoxNote.TabIndex = 35;
            richTextBoxNote.Text = "";
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new Point(398, 185);
            label1.Name = "label1";
            label1.Size = new Size(102, 43);
            label1.TabIndex = 36;
            label1.Text = "ملاحظة :";
            // 
            // buttonSave
            // 
            buttonSave.AutoSize = true;
            buttonSave.ForeColor = Color.Black;
            buttonSave.Image = Properties.Resources.diskette_489707;
            buttonSave.ImageAlign = ContentAlignment.MiddleLeft;
            buttonSave.Location = new Point(154, 458);
            buttonSave.Margin = new Padding(5);
            buttonSave.Name = "buttonSave";
            buttonSave.Size = new Size(234, 65);
            buttonSave.TabIndex = 9;
            buttonSave.Text = "حفظ";
            buttonSave.TextImageRelation = TextImageRelation.ImageBeforeText;
            buttonSave.UseVisualStyleBackColor = true;
            // 
            // AddMovementToTheVaultForm
            // 
            AutoScaleDimensions = new SizeF(168F, 168F);
            AutoScaleMode = AutoScaleMode.Dpi;
            AutoSize = true;
            ClientSize = new Size(543, 549);
            Controls.Add(buttonSave);
            Controls.Add(groupBox2);
            Font = new Font("Cairo", 12F, FontStyle.Regular, GraphicsUnit.Point, 0);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "AddMovementToTheVaultForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "اضافة حركة الى الخزينة";
            groupBox2.ResumeLayout(false);
            groupBox2.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private GroupBox groupBox2;
        private Label label5;
        private DateTimePicker dateTimePicker;
        private Label label4;
        private TextBox textBoxValue;
        private Label label3;
        private RadioButton radioButtonWithdrawMoneyFromStore;
        private RadioButton radioButtonAddMoneyToStore;
        private Label label1;
        private RichTextBox richTextBoxNote;
        private Button buttonSave;
    }
}