﻿namespace EMS.Gui.ProductsGui
{
    partial class AddCategoryForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            label4 = new Label();
            textBoxCategoryName = new TextBox();
            label3 = new Label();
            buttonAddCategory = new Button();
            SuspendLayout();
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.ForeColor = Color.Red;
            label4.Location = new Point(183, 49);
            label4.Name = "label4";
            label4.Size = new Size(35, 53);
            label4.TabIndex = 13;
            label4.Text = "*";
            // 
            // textBoxCategoryName
            // 
            textBoxCategoryName.Font = new Font("Cairo", 9.857142F);
            textBoxCategoryName.Location = new Point(224, 53);
            textBoxCategoryName.Margin = new Padding(3, 8, 3, 3);
            textBoxCategoryName.Name = "textBoxCategoryName";
            textBoxCategoryName.RightToLeft = RightToLeft.No;
            textBoxCategoryName.Size = new Size(363, 51);
            textBoxCategoryName.TabIndex = 11;
            textBoxCategoryName.TextAlign = HorizontalAlignment.Center;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new Point(21, 49);
            label3.Name = "label3";
            label3.Size = new Size(168, 53);
            label3.TabIndex = 12;
            label3.Text = "اسم الصنف :";
            // 
            // buttonAddCategory
            // 
            buttonAddCategory.AutoSize = true;
            buttonAddCategory.ForeColor = Color.Black;
            buttonAddCategory.Image = Properties.Resources.addfile_11750436;
            buttonAddCategory.ImageAlign = ContentAlignment.MiddleLeft;
            buttonAddCategory.Location = new Point(236, 129);
            buttonAddCategory.Margin = new Padding(5);
            buttonAddCategory.Name = "buttonAddCategory";
            buttonAddCategory.Size = new Size(137, 63);
            buttonAddCategory.TabIndex = 28;
            buttonAddCategory.Text = "اضافة";
            buttonAddCategory.TextImageRelation = TextImageRelation.ImageBeforeText;
            buttonAddCategory.UseVisualStyleBackColor = true;
            // 
            // AddCategoryForm
            // 
            AutoScaleDimensions = new SizeF(168F, 168F);
            AutoScaleMode = AutoScaleMode.Dpi;
            AutoSize = true;
            ClientSize = new Size(608, 231);
            Controls.Add(buttonAddCategory);
            Controls.Add(label4);
            Controls.Add(textBoxCategoryName);
            Controls.Add(label3);
            Font = new Font("Cairo", 12F, FontStyle.Regular, GraphicsUnit.Point, 0);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "AddCategoryForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "اضافة صنف جديد";
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private Label label4;
        private TextBox textBoxCategoryName;
        private Label label3;
        private Button buttonAddCategory;
    }
}