using System;
using System.Collections.Generic;
using System.Data.SQLite;
using EMS.Code.Models;

namespace EMS.Code.Data
{
    public class CategoryRepository
    {
        public List<Category> GetAll()
        {
            var categories = new List<Category>();
            
            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                string query = "SELECT Id, Name, Description, CreatedDate FROM Categories ORDER BY Name";
                
                using (var command = new SQLiteCommand(query, connection))
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        categories.Add(new Category
                        {
                            Id = reader.GetInt32(0),
                            Name = reader.GetString(1),
                            Description = reader.IsDBNull(2) ? null : reader.GetString(2),
                            CreatedDate = reader.GetDateTime(3)
                        });
                    }
                }
            }
            
            return categories;
        }

        public int Add(Category category)
        {
            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                string query = @"
                    INSERT INTO Categories (Name, Description) 
                    VALUES (@Name, @Description);
                    SELECT last_insert_rowid();";
                
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Name", category.Name);
                    command.Parameters.AddWithValue("@Description", category.Description ?? (object)DBNull.Value);
                    
                    return Convert.ToInt32(command.ExecuteScalar());
                }
            }
        }

        public bool Update(Category category)
        {
            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                string query = @"
                    UPDATE Categories 
                    SET Name = @Name, Description = @Description 
                    WHERE Id = @Id";
                
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", category.Id);
                    command.Parameters.AddWithValue("@Name", category.Name);
                    command.Parameters.AddWithValue("@Description", category.Description ?? (object)DBNull.Value);
                    
                    return command.ExecuteNonQuery() > 0;
                }
            }
        }

        public bool Delete(int id)
        {
            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                string query = "DELETE FROM Categories WHERE Id = @Id";
                
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", id);
                    return command.ExecuteNonQuery() > 0;
                }
            }
        }
    }
}
