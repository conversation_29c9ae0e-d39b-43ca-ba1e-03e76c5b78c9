﻿namespace EMS.Gui.OutcomeGui
{
    partial class AddOutcomeForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            buttonSave = new Button();
            groupBox2 = new GroupBox();
            label5 = new Label();
            dateTimePicker = new DateTimePicker();
            label7 = new Label();
            textBoxValue = new TextBox();
            label6 = new Label();
            label4 = new Label();
            textBoxName = new TextBox();
            label3 = new Label();
            groupBox2.SuspendLayout();
            SuspendLayout();
            // 
            // buttonSave
            // 
            buttonSave.AutoSize = true;
            buttonSave.ForeColor = Color.Black;
            buttonSave.Image = Properties.Resources.diskette_489707;
            buttonSave.ImageAlign = ContentAlignment.MiddleLeft;
            buttonSave.Location = new Point(184, 334);
            buttonSave.Margin = new Padding(5);
            buttonSave.Name = "buttonSave";
            buttonSave.Size = new Size(234, 65);
            buttonSave.TabIndex = 10;
            buttonSave.Text = "حفظ";
            buttonSave.TextImageRelation = TextImageRelation.ImageBeforeText;
            buttonSave.UseVisualStyleBackColor = true;
            // 
            // groupBox2
            // 
            groupBox2.AutoSize = true;
            groupBox2.Controls.Add(label5);
            groupBox2.Controls.Add(dateTimePicker);
            groupBox2.Controls.Add(label7);
            groupBox2.Controls.Add(textBoxValue);
            groupBox2.Controls.Add(label6);
            groupBox2.Controls.Add(label4);
            groupBox2.Controls.Add(textBoxName);
            groupBox2.Controls.Add(label3);
            groupBox2.Font = new Font("Cairo", 9.857142F, FontStyle.Regular, GraphicsUnit.Point, 0);
            groupBox2.Location = new Point(12, 12);
            groupBox2.Name = "groupBox2";
            groupBox2.Size = new Size(577, 303);
            groupBox2.TabIndex = 9;
            groupBox2.TabStop = false;
            groupBox2.Text = "بيانات التكلفة";
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Location = new Point(485, 208);
            label5.Name = "label5";
            label5.Size = new Size(83, 43);
            label5.TabIndex = 32;
            label5.Text = "التاريخ :";
            // 
            // dateTimePicker
            // 
            dateTimePicker.Format = DateTimePickerFormat.Short;
            dateTimePicker.Location = new Point(132, 202);
            dateTimePicker.Name = "dateTimePicker";
            dateTimePicker.Size = new Size(273, 51);
            dateTimePicker.TabIndex = 31;
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.ForeColor = Color.Red;
            label7.Location = new Point(441, 129);
            label7.Name = "label7";
            label7.Size = new Size(29, 43);
            label7.TabIndex = 16;
            label7.Text = "*";
            // 
            // textBoxValue
            // 
            textBoxValue.Font = new Font("Cairo", 9.857142F);
            textBoxValue.Location = new Point(22, 126);
            textBoxValue.Margin = new Padding(3, 8, 3, 3);
            textBoxValue.Name = "textBoxValue";
            textBoxValue.RightToLeft = RightToLeft.No;
            textBoxValue.Size = new Size(402, 51);
            textBoxValue.TabIndex = 3;
            textBoxValue.TextAlign = HorizontalAlignment.Center;
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Location = new Point(476, 129);
            label6.Name = "label6";
            label6.Size = new Size(92, 43);
            label6.TabIndex = 11;
            label6.Text = "القيمة :";
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.ForeColor = Color.Red;
            label4.Location = new Point(454, 67);
            label4.Name = "label4";
            label4.Size = new Size(29, 43);
            label4.TabIndex = 10;
            label4.Text = "*";
            // 
            // textBoxName
            // 
            textBoxName.Font = new Font("Cairo", 9.857142F);
            textBoxName.Location = new Point(22, 63);
            textBoxName.Margin = new Padding(3, 8, 3, 3);
            textBoxName.Name = "textBoxName";
            textBoxName.RightToLeft = RightToLeft.No;
            textBoxName.Size = new Size(402, 51);
            textBoxName.TabIndex = 2;
            textBoxName.TextAlign = HorizontalAlignment.Center;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new Point(487, 66);
            label3.Name = "label3";
            label3.Size = new Size(84, 43);
            label3.TabIndex = 8;
            label3.Text = "الاسم :";
            // 
            // AddOutcomeForm
            // 
            AutoScaleDimensions = new SizeF(168F, 168F);
            AutoScaleMode = AutoScaleMode.Dpi;
            AutoSize = true;
            ClientSize = new Size(602, 426);
            Controls.Add(buttonSave);
            Controls.Add(groupBox2);
            Font = new Font("Cairo", 12F, FontStyle.Regular, GraphicsUnit.Point, 0);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "AddOutcomeForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "اضافة تكلفة";
            groupBox2.ResumeLayout(false);
            groupBox2.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private Button buttonSave;
        private GroupBox groupBox2;
        private Label label5;
        private DateTimePicker dateTimePicker;
        private Label label7;
        private TextBox textBoxValue;
        private Label label6;
        private Label label4;
        private TextBox textBoxName;
        private Label label3;
    }
}