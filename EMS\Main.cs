using EMS.Code.Helper;
using EMS.Gui.HomeGui;
using EMS.Gui.ProductsGui;
using EMS.Gui.CustomersAndSuppliersGui;
using EMS.Gui.SalesGui;
using EMS.Gui.IncomsGui;
using EMS.Gui.OutcomeGui;
using EMS.Gui.StoreGui;
using EMS.Gui.UsersGui;

namespace EMS
{
    public partial class Main : Form
    {
        private PageHelper pageHelper;

        public Main()
        {
            InitializeComponent();
            pageHelper = new PageHelper(this);
            SetupEventHandlers();
            LoadHomePage();
        }

        private void SetupEventHandlers()
        {
            buttonHome.Click += ButtonHome_Click;
            buttonProducts.Click += ButtonProducts_Click;
            buttonCustomersAndSuppliers.Click += ButtonCustomersAndSuppliers_Click;
            buttonSales.Click += ButtonSales_Click;
            buttonIncoms.Click += ButtonIncoms_Click;
            buttonOutcome.Click += ButtonOutcome_Click;
            buttonStore.Click += ButtonStore_Click;
            buttonUsers.Click += ButtonUsers_Click;
        }

        private void LoadHomePage()
        {
            pageHelper.SetPage(new HomeUserControl());
        }

        private void ButtonHome_Click(object? sender, EventArgs e)
        {
            pageHelper.SetPage(new HomeUserControl());
        }

        private void ButtonProducts_Click(object? sender, EventArgs e)
        {
            pageHelper.SetPage(new ProductsUserControl());
        }

        private void ButtonCustomersAndSuppliers_Click(object? sender, EventArgs e)
        {
            pageHelper.SetPage(new CustomersAndSuppliersUserControl());
        }

        private void ButtonSales_Click(object? sender, EventArgs e)
        {
            pageHelper.SetPage(new SalesUserControl());
        }

        private void ButtonIncoms_Click(object? sender, EventArgs e)
        {
            pageHelper.SetPage(new IncomsUserControl());
        }

        private void ButtonOutcome_Click(object? sender, EventArgs e)
        {
            pageHelper.SetPage(new OutcomeUserControl());
        }

        private void ButtonStore_Click(object? sender, EventArgs e)
        {
            pageHelper.SetPage(new StoreUserControl());
        }

        private void ButtonUsers_Click(object? sender, EventArgs e)
        {
            pageHelper.SetPage(new UsersUserControl());
        }
    }
}
