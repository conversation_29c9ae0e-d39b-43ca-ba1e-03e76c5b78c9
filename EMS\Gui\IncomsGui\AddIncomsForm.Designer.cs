﻿namespace EMS.Gui.IncomsGui
{
    partial class AddIncomsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            groupBox2 = new GroupBox();
            label5 = new Label();
            dateTimePicker = new DateTimePicker();
            buttonAddSupplier = new Button();
            comboBoxSupplier = new ComboBox();
            label2 = new Label();
            buttonAddCustomer = new Button();
            comboBoxCustomer = new ComboBox();
            label1 = new Label();
            label7 = new Label();
            textBoxSalePrice = new TextBox();
            label8 = new Label();
            textBoxBuyPrice = new TextBox();
            label6 = new Label();
            label4 = new Label();
            textBoxName = new TextBox();
            label3 = new Label();
            buttonAdd = new Button();
            groupBox2.SuspendLayout();
            SuspendLayout();
            // 
            // groupBox2
            // 
            groupBox2.AutoSize = true;
            groupBox2.Controls.Add(label5);
            groupBox2.Controls.Add(dateTimePicker);
            groupBox2.Controls.Add(buttonAddSupplier);
            groupBox2.Controls.Add(comboBoxSupplier);
            groupBox2.Controls.Add(label2);
            groupBox2.Controls.Add(buttonAddCustomer);
            groupBox2.Controls.Add(comboBoxCustomer);
            groupBox2.Controls.Add(label1);
            groupBox2.Controls.Add(label7);
            groupBox2.Controls.Add(textBoxSalePrice);
            groupBox2.Controls.Add(label8);
            groupBox2.Controls.Add(textBoxBuyPrice);
            groupBox2.Controls.Add(label6);
            groupBox2.Controls.Add(label4);
            groupBox2.Controls.Add(textBoxName);
            groupBox2.Controls.Add(label3);
            groupBox2.Font = new Font("Cairo", 9.857142F, FontStyle.Regular, GraphicsUnit.Point, 0);
            groupBox2.Location = new Point(12, 12);
            groupBox2.Name = "groupBox2";
            groupBox2.Size = new Size(586, 492);
            groupBox2.TabIndex = 2;
            groupBox2.TabStop = false;
            groupBox2.Text = "بيانات الدخل";
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Location = new Point(493, 397);
            label5.Name = "label5";
            label5.Size = new Size(83, 43);
            label5.TabIndex = 32;
            label5.Text = "التاريخ :";
            // 
            // dateTimePicker
            // 
            dateTimePicker.Format = DateTimePickerFormat.Short;
            dateTimePicker.Location = new Point(143, 391);
            dateTimePicker.Name = "dateTimePicker";
            dateTimePicker.Size = new Size(273, 51);
            dateTimePicker.TabIndex = 31;
            // 
            // buttonAddSupplier
            // 
            buttonAddSupplier.AutoSize = true;
            buttonAddSupplier.ForeColor = Color.Black;
            buttonAddSupplier.Image = Properties.Resources.addfile_11750436;
            buttonAddSupplier.ImageAlign = ContentAlignment.MiddleLeft;
            buttonAddSupplier.Location = new Point(14, 189);
            buttonAddSupplier.Margin = new Padding(5);
            buttonAddSupplier.Name = "buttonAddSupplier";
            buttonAddSupplier.Size = new Size(120, 53);
            buttonAddSupplier.TabIndex = 30;
            buttonAddSupplier.Text = "اضافة";
            buttonAddSupplier.TextImageRelation = TextImageRelation.ImageBeforeText;
            buttonAddSupplier.UseVisualStyleBackColor = true;
            // 
            // comboBoxSupplier
            // 
            comboBoxSupplier.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBoxSupplier.FormattingEnabled = true;
            comboBoxSupplier.Location = new Point(143, 191);
            comboBoxSupplier.Name = "comboBoxSupplier";
            comboBoxSupplier.Size = new Size(273, 51);
            comboBoxSupplier.TabIndex = 29;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new Point(489, 194);
            label2.Name = "label2";
            label2.Size = new Size(87, 43);
            label2.TabIndex = 28;
            label2.Text = "المورد :";
            // 
            // buttonAddCustomer
            // 
            buttonAddCustomer.AutoSize = true;
            buttonAddCustomer.ForeColor = Color.Black;
            buttonAddCustomer.Image = Properties.Resources.addfile_11750436;
            buttonAddCustomer.ImageAlign = ContentAlignment.MiddleLeft;
            buttonAddCustomer.Location = new Point(14, 323);
            buttonAddCustomer.Margin = new Padding(5);
            buttonAddCustomer.Name = "buttonAddCustomer";
            buttonAddCustomer.Size = new Size(120, 53);
            buttonAddCustomer.TabIndex = 27;
            buttonAddCustomer.Text = "اضافة";
            buttonAddCustomer.TextImageRelation = TextImageRelation.ImageBeforeText;
            buttonAddCustomer.UseVisualStyleBackColor = true;
            // 
            // comboBoxCustomer
            // 
            comboBoxCustomer.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBoxCustomer.FormattingEnabled = true;
            comboBoxCustomer.Location = new Point(143, 325);
            comboBoxCustomer.Name = "comboBoxCustomer";
            comboBoxCustomer.Size = new Size(273, 51);
            comboBoxCustomer.TabIndex = 26;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new Point(481, 328);
            label1.Name = "label1";
            label1.Size = new Size(95, 43);
            label1.TabIndex = 21;
            label1.Text = "العميل :";
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.ForeColor = Color.Red;
            label7.Location = new Point(425, 258);
            label7.Name = "label7";
            label7.Size = new Size(29, 43);
            label7.TabIndex = 16;
            label7.Text = "*";
            // 
            // textBoxSalePrice
            // 
            textBoxSalePrice.Font = new Font("Cairo", 9.857142F);
            textBoxSalePrice.Location = new Point(14, 255);
            textBoxSalePrice.Margin = new Padding(3, 8, 3, 3);
            textBoxSalePrice.Name = "textBoxSalePrice";
            textBoxSalePrice.RightToLeft = RightToLeft.No;
            textBoxSalePrice.Size = new Size(402, 51);
            textBoxSalePrice.TabIndex = 4;
            textBoxSalePrice.TextAlign = HorizontalAlignment.Center;
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Location = new Point(460, 258);
            label8.Name = "label8";
            label8.Size = new Size(116, 43);
            label8.TabIndex = 14;
            label8.Text = "سعر البيع :";
            // 
            // textBoxBuyPrice
            // 
            textBoxBuyPrice.Font = new Font("Cairo", 9.857142F);
            textBoxBuyPrice.Location = new Point(14, 126);
            textBoxBuyPrice.Margin = new Padding(3, 8, 3, 3);
            textBoxBuyPrice.Name = "textBoxBuyPrice";
            textBoxBuyPrice.RightToLeft = RightToLeft.No;
            textBoxBuyPrice.Size = new Size(402, 51);
            textBoxBuyPrice.TabIndex = 3;
            textBoxBuyPrice.TextAlign = HorizontalAlignment.Center;
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Location = new Point(444, 129);
            label6.Name = "label6";
            label6.Size = new Size(132, 43);
            label6.TabIndex = 11;
            label6.Text = "سعر الشراء :";
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.ForeColor = Color.Red;
            label4.Location = new Point(459, 67);
            label4.Name = "label4";
            label4.Size = new Size(29, 43);
            label4.TabIndex = 10;
            label4.Text = "*";
            // 
            // textBoxName
            // 
            textBoxName.Font = new Font("Cairo", 9.857142F);
            textBoxName.Location = new Point(14, 63);
            textBoxName.Margin = new Padding(3, 8, 3, 3);
            textBoxName.Name = "textBoxName";
            textBoxName.RightToLeft = RightToLeft.No;
            textBoxName.Size = new Size(402, 51);
            textBoxName.TabIndex = 2;
            textBoxName.TextAlign = HorizontalAlignment.Center;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new Point(492, 66);
            label3.Name = "label3";
            label3.Size = new Size(84, 43);
            label3.TabIndex = 8;
            label3.Text = "الاسم :";
            // 
            // buttonAdd
            // 
            buttonAdd.AutoSize = true;
            buttonAdd.ForeColor = Color.Black;
            buttonAdd.Image = Properties.Resources.addfile_11750436;
            buttonAdd.ImageAlign = ContentAlignment.MiddleLeft;
            buttonAdd.Location = new Point(195, 521);
            buttonAdd.Margin = new Padding(5);
            buttonAdd.Name = "buttonAdd";
            buttonAdd.Size = new Size(218, 65);
            buttonAdd.TabIndex = 9;
            buttonAdd.Text = "اضافة";
            buttonAdd.TextImageRelation = TextImageRelation.ImageBeforeText;
            buttonAdd.UseVisualStyleBackColor = true;
            // 
            // AddIncomsForm
            // 
            AutoScaleDimensions = new SizeF(168F, 168F);
            AutoScaleMode = AutoScaleMode.Dpi;
            AutoSize = true;
            ClientSize = new Size(608, 606);
            Controls.Add(buttonAdd);
            Controls.Add(groupBox2);
            Font = new Font("Cairo", 12F, FontStyle.Regular, GraphicsUnit.Point, 0);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "AddIncomsForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "اضافة دخل";
            groupBox2.ResumeLayout(false);
            groupBox2.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private GroupBox groupBox2;
        private Button buttonAddSupplier;
        private ComboBox comboBoxSupplier;
        private Label label2;
        private Button buttonAddCustomer;
        private ComboBox comboBoxCustomer;
        private Label label1;
        private Label label7;
        private TextBox textBoxSalePrice;
        private Label label8;
        private TextBox textBoxBuyPrice;
        private Label label6;
        private Label label4;
        private TextBox textBoxName;
        private Label label3;
        private DateTimePicker dateTimePicker;
        private Label label5;
        private Button buttonAdd;
    }
}