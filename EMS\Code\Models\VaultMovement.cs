using System;

namespace EMS.Code.Models
{
    public class VaultMovement
    {
        public int Id { get; set; }
        public string Type { get; set; } = string.Empty; // "إيداع" أو "سحب"
        public decimal Amount { get; set; }
        public string? Description { get; set; }
        public DateTime Date { get; set; }
        public int? UserId { get; set; }
        
        // Navigation property
        public User? User { get; set; }
    }
}
