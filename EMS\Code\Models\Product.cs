using System;

namespace EMS.Code.Models
{
    public class Product
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Barcode { get; set; }
        public string? Barcode2 { get; set; }
        public string? Barcode3 { get; set; }
        public int? CategoryId { get; set; }
        public decimal BuyPrice { get; set; }
        public decimal SalePrice { get; set; }
        public int Quantity { get; set; }
        public int ReorderPoint { get; set; }
        public byte[]? Image { get; set; }
        public DateTime CreatedDate { get; set; }
        
        // Navigation property
        public Category? Category { get; set; }
    }
}
