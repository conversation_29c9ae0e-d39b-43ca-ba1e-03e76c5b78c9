﻿namespace EMS.Gui.ProductsGui
{
    partial class AddProductForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            groupBox1 = new GroupBox();
            buttonAdd = new Button();
            pictureBoxProduct = new PictureBox();
            groupBox2 = new GroupBox();
            buttonAddCategory = new Button();
            comboBoxCategory = new ComboBox();
            textBoxBarcode3 = new TextBox();
            label12 = new Label();
            textBoxBarcode2 = new TextBox();
            label11 = new Label();
            label1 = new Label();
            textBoxReorderPoint = new TextBox();
            label10 = new Label();
            textBoxQuantity = new TextBox();
            label9 = new Label();
            label7 = new Label();
            textBoxSalePrice = new TextBox();
            label8 = new Label();
            label5 = new Label();
            textBoxBuyPrice = new TextBox();
            label6 = new Label();
            label4 = new Label();
            textBoxName = new TextBox();
            label3 = new Label();
            textBoxBarcode = new TextBox();
            label2 = new Label();
            buttonSave = new Button();
            groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBoxProduct).BeginInit();
            groupBox2.SuspendLayout();
            SuspendLayout();
            // 
            // groupBox1
            // 
            groupBox1.AutoSize = true;
            groupBox1.Controls.Add(buttonAdd);
            groupBox1.Controls.Add(pictureBoxProduct);
            groupBox1.Location = new Point(12, 12);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new Size(365, 494);
            groupBox1.TabIndex = 0;
            groupBox1.TabStop = false;
            groupBox1.Text = "صورة المنتج";
            // 
            // buttonAdd
            // 
            buttonAdd.AutoSize = true;
            buttonAdd.ForeColor = Color.Black;
            buttonAdd.Image = Properties.Resources.addfile_11750436;
            buttonAdd.ImageAlign = ContentAlignment.MiddleLeft;
            buttonAdd.Location = new Point(80, 368);
            buttonAdd.Margin = new Padding(5);
            buttonAdd.Name = "buttonAdd";
            buttonAdd.Size = new Size(205, 65);
            buttonAdd.TabIndex = 7;
            buttonAdd.Text = "اضافة صورة";
            buttonAdd.TextImageRelation = TextImageRelation.ImageBeforeText;
            buttonAdd.UseVisualStyleBackColor = true;
            // 
            // pictureBoxProduct
            // 
            pictureBoxProduct.BackColor = SystemColors.ControlLight;
            pictureBoxProduct.Location = new Point(24, 76);
            pictureBoxProduct.Name = "pictureBoxProduct";
            pictureBoxProduct.Size = new Size(325, 272);
            pictureBoxProduct.TabIndex = 0;
            pictureBoxProduct.TabStop = false;
            // 
            // groupBox2
            // 
            groupBox2.AutoSize = true;
            groupBox2.Controls.Add(buttonAddCategory);
            groupBox2.Controls.Add(comboBoxCategory);
            groupBox2.Controls.Add(textBoxBarcode3);
            groupBox2.Controls.Add(label12);
            groupBox2.Controls.Add(textBoxBarcode2);
            groupBox2.Controls.Add(label11);
            groupBox2.Controls.Add(label1);
            groupBox2.Controls.Add(textBoxReorderPoint);
            groupBox2.Controls.Add(label10);
            groupBox2.Controls.Add(textBoxQuantity);
            groupBox2.Controls.Add(label9);
            groupBox2.Controls.Add(label7);
            groupBox2.Controls.Add(textBoxSalePrice);
            groupBox2.Controls.Add(label8);
            groupBox2.Controls.Add(label5);
            groupBox2.Controls.Add(textBoxBuyPrice);
            groupBox2.Controls.Add(label6);
            groupBox2.Controls.Add(label4);
            groupBox2.Controls.Add(textBoxName);
            groupBox2.Controls.Add(label3);
            groupBox2.Controls.Add(textBoxBarcode);
            groupBox2.Controls.Add(label2);
            groupBox2.Font = new Font("Cairo", 9.857142F, FontStyle.Regular, GraphicsUnit.Point, 0);
            groupBox2.Location = new Point(383, 17);
            groupBox2.Name = "groupBox2";
            groupBox2.Size = new Size(615, 610);
            groupBox2.TabIndex = 1;
            groupBox2.TabStop = false;
            groupBox2.Text = "بيانات المنتج";
            // 
            // buttonAddCategory
            // 
            buttonAddCategory.AutoSize = true;
            buttonAddCategory.ForeColor = Color.Black;
            buttonAddCategory.Image = Properties.Resources.addfile_11750436;
            buttonAddCategory.ImageAlign = ContentAlignment.MiddleLeft;
            buttonAddCategory.Location = new Point(59, 505);
            buttonAddCategory.Margin = new Padding(5);
            buttonAddCategory.Name = "buttonAddCategory";
            buttonAddCategory.Size = new Size(120, 53);
            buttonAddCategory.TabIndex = 27;
            buttonAddCategory.Text = "اضافة";
            buttonAddCategory.TextImageRelation = TextImageRelation.ImageBeforeText;
            buttonAddCategory.UseVisualStyleBackColor = true;
            // 
            // comboBoxCategory
            // 
            comboBoxCategory.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBoxCategory.FormattingEnabled = true;
            comboBoxCategory.Items.AddRange(new object[] { "بدون صنف" });
            comboBoxCategory.Location = new Point(188, 507);
            comboBoxCategory.Name = "comboBoxCategory";
            comboBoxCategory.Size = new Size(262, 51);
            comboBoxCategory.TabIndex = 26;
            // 
            // textBoxBarcode3
            // 
            textBoxBarcode3.Font = new Font("Cairo", 9.857142F);
            textBoxBarcode3.Location = new Point(188, 445);
            textBoxBarcode3.Margin = new Padding(3, 8, 3, 3);
            textBoxBarcode3.Name = "textBoxBarcode3";
            textBoxBarcode3.RightToLeft = RightToLeft.No;
            textBoxBarcode3.Size = new Size(262, 51);
            textBoxBarcode3.TabIndex = 24;
            textBoxBarcode3.TextAlign = HorizontalAlignment.Center;
            // 
            // label12
            // 
            label12.AutoSize = true;
            label12.Location = new Point(472, 448);
            label12.Name = "label12";
            label12.Size = new Size(117, 43);
            label12.TabIndex = 25;
            label12.Text = "الباركود 3 :";
            // 
            // textBoxBarcode2
            // 
            textBoxBarcode2.Font = new Font("Cairo", 9.857142F);
            textBoxBarcode2.Location = new Point(188, 383);
            textBoxBarcode2.Margin = new Padding(3, 8, 3, 3);
            textBoxBarcode2.Name = "textBoxBarcode2";
            textBoxBarcode2.RightToLeft = RightToLeft.No;
            textBoxBarcode2.Size = new Size(262, 51);
            textBoxBarcode2.TabIndex = 22;
            textBoxBarcode2.TextAlign = HorizontalAlignment.Center;
            // 
            // label11
            // 
            label11.AutoSize = true;
            label11.Location = new Point(472, 386);
            label11.Name = "label11";
            label11.Size = new Size(117, 43);
            label11.TabIndex = 23;
            label11.Text = "الباركود 2 :";
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new Point(497, 510);
            label1.Name = "label1";
            label1.Size = new Size(92, 43);
            label1.TabIndex = 21;
            label1.Text = "الصنف :";
            // 
            // textBoxReorderPoint
            // 
            textBoxReorderPoint.Font = new Font("Cairo", 9.857142F);
            textBoxReorderPoint.Location = new Point(7, 261);
            textBoxReorderPoint.Margin = new Padding(3, 8, 3, 3);
            textBoxReorderPoint.Name = "textBoxReorderPoint";
            textBoxReorderPoint.RightToLeft = RightToLeft.No;
            textBoxReorderPoint.Size = new Size(172, 51);
            textBoxReorderPoint.TabIndex = 6;
            textBoxReorderPoint.TextAlign = HorizontalAlignment.Center;
            // 
            // label10
            // 
            label10.AutoSize = true;
            label10.Location = new Point(185, 264);
            label10.Name = "label10";
            label10.Size = new Size(114, 43);
            label10.TabIndex = 19;
            label10.Text = "حد الطلب :";
            // 
            // textBoxQuantity
            // 
            textBoxQuantity.Font = new Font("Cairo", 9.857142F);
            textBoxQuantity.Location = new Point(305, 261);
            textBoxQuantity.Margin = new Padding(3, 8, 3, 3);
            textBoxQuantity.Name = "textBoxQuantity";
            textBoxQuantity.RightToLeft = RightToLeft.No;
            textBoxQuantity.Size = new Size(186, 51);
            textBoxQuantity.TabIndex = 5;
            textBoxQuantity.TextAlign = HorizontalAlignment.Center;
            // 
            // label9
            // 
            label9.AutoSize = true;
            label9.Location = new Point(497, 264);
            label9.Name = "label9";
            label9.Size = new Size(92, 43);
            label9.TabIndex = 17;
            label9.Text = "الكمية :";
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.ForeColor = Color.Red;
            label7.Location = new Point(415, 191);
            label7.Name = "label7";
            label7.Size = new Size(29, 43);
            label7.TabIndex = 16;
            label7.Text = "*";
            // 
            // textBoxSalePrice
            // 
            textBoxSalePrice.Font = new Font("Cairo", 9.857142F);
            textBoxSalePrice.Location = new Point(7, 188);
            textBoxSalePrice.Margin = new Padding(3, 8, 3, 3);
            textBoxSalePrice.Name = "textBoxSalePrice";
            textBoxSalePrice.RightToLeft = RightToLeft.No;
            textBoxSalePrice.Size = new Size(402, 51);
            textBoxSalePrice.TabIndex = 4;
            textBoxSalePrice.TextAlign = HorizontalAlignment.Center;
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Location = new Point(473, 191);
            label8.Name = "label8";
            label8.Size = new Size(116, 43);
            label8.TabIndex = 14;
            label8.Text = "سعر البيع :";
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.ForeColor = Color.Red;
            label5.Location = new Point(414, 129);
            label5.Name = "label5";
            label5.Size = new Size(29, 43);
            label5.TabIndex = 13;
            label5.Text = "*";
            // 
            // textBoxBuyPrice
            // 
            textBoxBuyPrice.Font = new Font("Cairo", 9.857142F);
            textBoxBuyPrice.Location = new Point(7, 126);
            textBoxBuyPrice.Margin = new Padding(3, 8, 3, 3);
            textBoxBuyPrice.Name = "textBoxBuyPrice";
            textBoxBuyPrice.RightToLeft = RightToLeft.No;
            textBoxBuyPrice.Size = new Size(402, 51);
            textBoxBuyPrice.TabIndex = 3;
            textBoxBuyPrice.TextAlign = HorizontalAlignment.Center;
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Location = new Point(457, 129);
            label6.Name = "label6";
            label6.Size = new Size(132, 43);
            label6.TabIndex = 11;
            label6.Text = "سعر الشراء :";
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.ForeColor = Color.Red;
            label4.Location = new Point(480, 67);
            label4.Name = "label4";
            label4.Size = new Size(29, 43);
            label4.TabIndex = 10;
            label4.Text = "*";
            // 
            // textBoxName
            // 
            textBoxName.Font = new Font("Cairo", 9.857142F);
            textBoxName.Location = new Point(7, 63);
            textBoxName.Margin = new Padding(3, 8, 3, 3);
            textBoxName.Name = "textBoxName";
            textBoxName.RightToLeft = RightToLeft.No;
            textBoxName.Size = new Size(467, 51);
            textBoxName.TabIndex = 2;
            textBoxName.TextAlign = HorizontalAlignment.Center;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new Point(505, 66);
            label3.Name = "label3";
            label3.Size = new Size(84, 43);
            label3.TabIndex = 8;
            label3.Text = "الاسم :";
            // 
            // textBoxBarcode
            // 
            textBoxBarcode.Font = new Font("Cairo", 9.857142F);
            textBoxBarcode.Location = new Point(188, 323);
            textBoxBarcode.Margin = new Padding(3, 8, 3, 3);
            textBoxBarcode.Name = "textBoxBarcode";
            textBoxBarcode.RightToLeft = RightToLeft.No;
            textBoxBarcode.Size = new Size(262, 51);
            textBoxBarcode.TabIndex = 1;
            textBoxBarcode.TextAlign = HorizontalAlignment.Center;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new Point(490, 326);
            label2.Name = "label2";
            label2.Size = new Size(99, 43);
            label2.TabIndex = 6;
            label2.Text = "الباركود :";
            // 
            // buttonSave
            // 
            buttonSave.AutoSize = true;
            buttonSave.ForeColor = Color.Black;
            buttonSave.Image = Properties.Resources.diskette_489707;
            buttonSave.ImageAlign = ContentAlignment.MiddleLeft;
            buttonSave.Location = new Point(12, 514);
            buttonSave.Margin = new Padding(5);
            buttonSave.Name = "buttonSave";
            buttonSave.Size = new Size(365, 111);
            buttonSave.TabIndex = 8;
            buttonSave.Text = "حفظ";
            buttonSave.TextImageRelation = TextImageRelation.ImageBeforeText;
            buttonSave.UseVisualStyleBackColor = true;
            // 
            // AddProductForm
            // 
            AutoScaleDimensions = new SizeF(168F, 168F);
            AutoScaleMode = AutoScaleMode.Dpi;
            AutoSize = true;
            ClientSize = new Size(1004, 639);
            Controls.Add(buttonSave);
            Controls.Add(groupBox2);
            Controls.Add(groupBox1);
            Font = new Font("Cairo", 12F, FontStyle.Regular, GraphicsUnit.Point, 0);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "AddProductForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "اضافة منتج";
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBoxProduct).EndInit();
            groupBox2.ResumeLayout(false);
            groupBox2.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private GroupBox groupBox1;
        private GroupBox groupBox2;
        private Label label4;
        private TextBox textBoxName;
        private Label label3;
        private TextBox textBoxBarcode;
        private Label label2;
        private Label label7;
        private TextBox textBoxSalePrice;
        private Label label8;
        private Label label5;
        private TextBox textBoxBuyPrice;
        private Label label6;
        private TextBox textBoxQuantity;
        private Label label9;
        private PictureBox pictureBoxProduct;
        private TextBox textBoxReorderPoint;
        private Label label10;
        private Button buttonAdd;
        private Button buttonSave;
        private TextBox textBoxBarcode2;
        private Label label11;
        private Label label1;
        private TextBox textBoxBarcode3;
        private Label label12;
        private Button buttonAddCategory;
        private ComboBox comboBoxCategory;
    }
}