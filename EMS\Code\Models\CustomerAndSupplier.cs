using System;

namespace EMS.Code.Models
{
    public class CustomerAndSupplier
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // "عميل" أو "مورد"
        public string? Phone { get; set; }
        public string? Address { get; set; }
        public decimal Balance { get; set; }
        public DateTime CreatedDate { get; set; }
    }
}
