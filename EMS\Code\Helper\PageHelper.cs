﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EMS.Code.Helper
{
    internal class PageHelper
    {
        private readonly Main main;

        public PageHelper(Main main)
        {
            this.main = main;
        }

        // set new page
        public void SetPage(UserControl pageUserControl)
        {
            // get the current page
            var oldPage = main.panelContainer.Controls.OfType<UserControl>().FirstOrDefault();

            // remove the current page
            if (oldPage != null && oldPage != pageUserControl)
            {
                main.panelContainer.Controls.Remove(oldPage);
                oldPage.Dispose();
            }

            // add the new page
            if (oldPage != pageUserControl)
            {
                pageUserControl.Dock = DockStyle.Fill;
                main.panelContainer.Controls.Add(pageUserControl);
            }
        }
    }
}
