using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using EMS.Code.Data;
using EMS.Code.Models;

namespace EMS.Gui.ProductsGui
{
    public partial class AddProductForm : Form
    {
        private SimpleProductRepository productRepository;
        private CategoryRepository categoryRepository;

        public AddProductForm(Main main)
        {
            InitializeComponent();
            productRepository = new SimpleProductRepository();
            categoryRepository = new CategoryRepository();
            LoadCategories();
            SetupEventHandlers();
        }

        private void LoadCategories()
        {
            try
            {
                var categories = categoryRepository.GetAll();
                comboBoxCategory.DataSource = categories;
                comboBoxCategory.DisplayMember = "Name";
                comboBoxCategory.ValueMember = "Id";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفئات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetupEventHandlers()
        {
            buttonSave.Click += ButtonSave_Click;
        }

        private void ButtonSave_Click(object? sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(textBoxName.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المنتج", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var product = new Product
                {
                    Name = textBoxName.Text.Trim(),
                    Barcode = string.IsNullOrWhiteSpace(textBoxBarcode.Text) ? null : textBoxBarcode.Text.Trim(),
                    BuyPrice = decimal.TryParse(textBoxBuyPrice.Text, out decimal buyPrice) ? buyPrice : 0,
                    SalePrice = decimal.TryParse(textBoxSalePrice.Text, out decimal salePrice) ? salePrice : 0,
                    Quantity = int.TryParse(textBoxQuantity.Text, out int quantity) ? quantity : 0
                };

                int productId = productRepository.Add(product);

                if (productId > 0)
                {
                    MessageBox.Show("تم حفظ المنتج بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ المنتج", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المنتج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
