using System;
using System.Collections.Generic;
using System.Data.SQLite;
using EMS.Code.Models;

namespace EMS.Code.Data
{
    public class SimpleProductRepository
    {
        public List<Product> GetAll()
        {
            var products = new List<Product>();
            
            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                string query = "SELECT * FROM Products ORDER BY Name";
                
                using (var command = new SQLiteCommand(query, connection))
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        products.Add(new Product
                        {
                            Id = Convert.ToInt32(reader["Id"]),
                            Name = reader["Name"].ToString() ?? "",
                            Barcode = reader["Barcode"] == DBNull.Value ? null : reader["Barcode"].ToString(),
                            BuyPrice = Convert.ToDecimal(reader["BuyPrice"]),
                            SalePrice = Convert.ToDecimal(reader["SalePrice"]),
                            Quantity = Convert.ToInt32(reader["Quantity"]),
                            CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                        });
                    }
                }
            }
            
            return products;
        }

        public int Add(Product product)
        {
            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                string query = @"
                    INSERT INTO Products (Name, Barcode, BuyPrice, SalePrice, Quantity) 
                    VALUES (@Name, @Barcode, @BuyPrice, @SalePrice, @Quantity);
                    SELECT last_insert_rowid();";
                
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Name", product.Name);
                    command.Parameters.AddWithValue("@Barcode", product.Barcode ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@BuyPrice", product.BuyPrice);
                    command.Parameters.AddWithValue("@SalePrice", product.SalePrice);
                    command.Parameters.AddWithValue("@Quantity", product.Quantity);
                    
                    return Convert.ToInt32(command.ExecuteScalar());
                }
            }
        }

        public bool Update(Product product)
        {
            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                string query = @"
                    UPDATE Products 
                    SET Name = @Name, Barcode = @Barcode, BuyPrice = @BuyPrice, 
                        SalePrice = @SalePrice, Quantity = @Quantity 
                    WHERE Id = @Id";
                
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", product.Id);
                    command.Parameters.AddWithValue("@Name", product.Name);
                    command.Parameters.AddWithValue("@Barcode", product.Barcode ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@BuyPrice", product.BuyPrice);
                    command.Parameters.AddWithValue("@SalePrice", product.SalePrice);
                    command.Parameters.AddWithValue("@Quantity", product.Quantity);
                    
                    return command.ExecuteNonQuery() > 0;
                }
            }
        }

        public bool Delete(int id)
        {
            using (var connection = DatabaseHelper.GetConnection())
            {
                connection.Open();
                string query = "DELETE FROM Products WHERE Id = @Id";
                
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", id);
                    return command.ExecuteNonQuery() > 0;
                }
            }
        }
    }
}
