﻿namespace EMS.Gui.CustomersAndSuppliersGui
{
    partial class AddCustomersAndSuppliersForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            groupBox2 = new GroupBox();
            comboBoxSuppliar = new ComboBox();
            label2 = new Label();
            textBoxSalePrice = new TextBox();
            label8 = new Label();
            label4 = new Label();
            textBoxName = new TextBox();
            label3 = new Label();
            textBox1 = new TextBox();
            label1 = new Label();
            buttonAdd = new Button();
            groupBox2.SuspendLayout();
            SuspendLayout();
            // 
            // groupBox2
            // 
            groupBox2.AutoSize = true;
            groupBox2.Controls.Add(textBox1);
            groupBox2.Controls.Add(label1);
            groupBox2.Controls.Add(comboBoxSuppliar);
            groupBox2.Controls.Add(label2);
            groupBox2.Controls.Add(textBoxSalePrice);
            groupBox2.Controls.Add(label8);
            groupBox2.Controls.Add(label4);
            groupBox2.Controls.Add(textBoxName);
            groupBox2.Controls.Add(label3);
            groupBox2.Font = new Font("Cairo", 9.857142F, FontStyle.Regular, GraphicsUnit.Point, 0);
            groupBox2.Location = new Point(12, 12);
            groupBox2.Name = "groupBox2";
            groupBox2.Size = new Size(502, 401);
            groupBox2.TabIndex = 3;
            groupBox2.TabStop = false;
            groupBox2.Text = "بيانات المورد او العميل";
            // 
            // comboBoxSuppliar
            // 
            comboBoxSuppliar.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBoxSuppliar.FormattingEnabled = true;
            comboBoxSuppliar.Items.AddRange(new object[] { "مورد", "عميل" });
            comboBoxSuppliar.Location = new Point(17, 59);
            comboBoxSuppliar.Name = "comboBoxSuppliar";
            comboBoxSuppliar.Size = new Size(347, 51);
            comboBoxSuppliar.TabIndex = 29;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new Point(412, 62);
            label2.Name = "label2";
            label2.Size = new Size(74, 43);
            label2.TabIndex = 28;
            label2.Text = "النوع :";
            // 
            // textBoxSalePrice
            // 
            textBoxSalePrice.Font = new Font("Cairo", 9.857142F);
            textBoxSalePrice.Location = new Point(17, 214);
            textBoxSalePrice.Margin = new Padding(3, 8, 3, 3);
            textBoxSalePrice.Name = "textBoxSalePrice";
            textBoxSalePrice.RightToLeft = RightToLeft.No;
            textBoxSalePrice.Size = new Size(347, 51);
            textBoxSalePrice.TabIndex = 4;
            textBoxSalePrice.TextAlign = HorizontalAlignment.Center;
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Location = new Point(391, 217);
            label8.Name = "label8";
            label8.Size = new Size(93, 43);
            label8.TabIndex = 14;
            label8.Text = "الهاتف :";
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.ForeColor = Color.Red;
            label4.Location = new Point(377, 138);
            label4.Name = "label4";
            label4.Size = new Size(29, 43);
            label4.TabIndex = 10;
            label4.Text = "*";
            // 
            // textBoxName
            // 
            textBoxName.Font = new Font("Cairo", 9.857142F);
            textBoxName.Location = new Point(17, 137);
            textBoxName.Margin = new Padding(3, 8, 3, 3);
            textBoxName.Name = "textBoxName";
            textBoxName.RightToLeft = RightToLeft.No;
            textBoxName.Size = new Size(347, 51);
            textBoxName.TabIndex = 2;
            textBoxName.TextAlign = HorizontalAlignment.Center;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new Point(402, 137);
            label3.Name = "label3";
            label3.Size = new Size(84, 43);
            label3.TabIndex = 8;
            label3.Text = "الاسم :";
            // 
            // textBox1
            // 
            textBox1.Font = new Font("Cairo", 9.857142F);
            textBox1.Location = new Point(17, 300);
            textBox1.Margin = new Padding(3, 8, 3, 3);
            textBox1.Name = "textBox1";
            textBox1.RightToLeft = RightToLeft.No;
            textBox1.Size = new Size(347, 51);
            textBox1.TabIndex = 30;
            textBox1.TextAlign = HorizontalAlignment.Center;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new Point(387, 303);
            label1.Name = "label1";
            label1.Size = new Size(97, 43);
            label1.TabIndex = 31;
            label1.Text = "العنوان :";
            // 
            // buttonAdd
            // 
            buttonAdd.AutoSize = true;
            buttonAdd.ForeColor = Color.Black;
            buttonAdd.Image = Properties.Resources.addfile_11750436;
            buttonAdd.ImageAlign = ContentAlignment.MiddleLeft;
            buttonAdd.Location = new Point(156, 423);
            buttonAdd.Margin = new Padding(5);
            buttonAdd.Name = "buttonAdd";
            buttonAdd.Size = new Size(218, 65);
            buttonAdd.TabIndex = 4;
            buttonAdd.Text = "اضافة";
            buttonAdd.TextImageRelation = TextImageRelation.ImageBeforeText;
            buttonAdd.UseVisualStyleBackColor = true;
            // 
            // AddCustomersAndSuppliersForm
            // 
            AutoScaleDimensions = new SizeF(168F, 168F);
            AutoScaleMode = AutoScaleMode.Dpi;
            AutoSize = true;
            ClientSize = new Size(531, 509);
            Controls.Add(buttonAdd);
            Controls.Add(groupBox2);
            Font = new Font("Cairo", 12F, FontStyle.Regular, GraphicsUnit.Point, 0);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "AddCustomersAndSuppliersForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "اضافة مورد او عميل";
            groupBox2.ResumeLayout(false);
            groupBox2.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private GroupBox groupBox2;
        private ComboBox comboBoxSuppliar;
        private Label label2;
        private TextBox textBoxSalePrice;
        private Label label8;
        private Label label4;
        private TextBox textBoxName;
        private Label label3;
        private TextBox textBox1;
        private Label label1;
        private Button buttonAdd;
    }
}