using System;
using System.Data.SQLite;
using System.IO;

namespace EMS.Code.Data
{
    public static class DatabaseHelper
    {
        private static readonly string DatabasePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "EMS.db");
        private static readonly string ConnectionString = $"Data Source={DatabasePath};Version=3;";

        public static void InitializeDatabase()
        {
            if (!File.Exists(DatabasePath))
            {
                SQLiteConnection.CreateFile(DatabasePath);
            }

            CreateTables();
        }

        public static SQLiteConnection GetConnection()
        {
            return new SQLiteConnection(ConnectionString);
        }

        private static void CreateTables()
        {
            using (var connection = GetConnection())
            {
                connection.Open();

                // جدول الفئات
                string createCategoriesTable = @"
                    CREATE TABLE IF NOT EXISTS Categories (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL,
                        Description TEXT,
                        CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP
                    )";

                // جدول المنتجات
                string createProductsTable = @"
                    CREATE TABLE IF NOT EXISTS Products (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL,
                        Barcode TEXT UNIQUE,
                        Barcode2 TEXT,
                        Barcode3 TEXT,
                        CategoryId INTEGER,
                        BuyPrice DECIMAL(10,2) DEFAULT 0,
                        SalePrice DECIMAL(10,2) DEFAULT 0,
                        Quantity INTEGER DEFAULT 0,
                        ReorderPoint INTEGER DEFAULT 0,
                        Image BLOB,
                        CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (CategoryId) REFERENCES Categories(Id)
                    )";

                // جدول العملاء والموردين
                string createCustomersAndSuppliersTable = @"
                    CREATE TABLE IF NOT EXISTS CustomersAndSuppliers (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL,
                        Type TEXT NOT NULL CHECK(Type IN ('عميل', 'مورد')),
                        Phone TEXT,
                        Address TEXT,
                        Balance DECIMAL(10,2) DEFAULT 0,
                        CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP
                    )";

                // جدول المستخدمين
                string createUsersTable = @"
                    CREATE TABLE IF NOT EXISTS Users (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Username TEXT UNIQUE NOT NULL,
                        Password TEXT NOT NULL,
                        FullName TEXT NOT NULL,
                        Role TEXT NOT NULL DEFAULT 'مستخدم',
                        IsActive BOOLEAN DEFAULT 1,
                        CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP
                    )";

                // جدول المبيعات
                string createSalesTable = @"
                    CREATE TABLE IF NOT EXISTS Sales (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        CustomerId INTEGER,
                        UserId INTEGER,
                        TotalAmount DECIMAL(10,2) NOT NULL,
                        PaidAmount DECIMAL(10,2) DEFAULT 0,
                        Discount DECIMAL(10,2) DEFAULT 0,
                        SaleDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                        Notes TEXT,
                        FOREIGN KEY (CustomerId) REFERENCES CustomersAndSuppliers(Id),
                        FOREIGN KEY (UserId) REFERENCES Users(Id)
                    )";

                // جدول تفاصيل المبيعات
                string createSaleDetailsTable = @"
                    CREATE TABLE IF NOT EXISTS SaleDetails (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        SaleId INTEGER NOT NULL,
                        ProductId INTEGER NOT NULL,
                        Quantity INTEGER NOT NULL,
                        UnitPrice DECIMAL(10,2) NOT NULL,
                        TotalPrice DECIMAL(10,2) NOT NULL,
                        FOREIGN KEY (SaleId) REFERENCES Sales(Id),
                        FOREIGN KEY (ProductId) REFERENCES Products(Id)
                    )";

                // جدول المشتريات
                string createPurchasesTable = @"
                    CREATE TABLE IF NOT EXISTS Purchases (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        SupplierId INTEGER,
                        UserId INTEGER,
                        TotalAmount DECIMAL(10,2) NOT NULL,
                        PaidAmount DECIMAL(10,2) DEFAULT 0,
                        PurchaseDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                        Notes TEXT,
                        FOREIGN KEY (SupplierId) REFERENCES CustomersAndSuppliers(Id),
                        FOREIGN KEY (UserId) REFERENCES Users(Id)
                    )";

                // جدول تفاصيل المشتريات
                string createPurchaseDetailsTable = @"
                    CREATE TABLE IF NOT EXISTS PurchaseDetails (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        PurchaseId INTEGER NOT NULL,
                        ProductId INTEGER NOT NULL,
                        Quantity INTEGER NOT NULL,
                        UnitPrice DECIMAL(10,2) NOT NULL,
                        TotalPrice DECIMAL(10,2) NOT NULL,
                        FOREIGN KEY (PurchaseId) REFERENCES Purchases(Id),
                        FOREIGN KEY (ProductId) REFERENCES Products(Id)
                    )";

                // جدول المداخيل الأخرى
                string createIncomesTable = @"
                    CREATE TABLE IF NOT EXISTS Incomes (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Description TEXT NOT NULL,
                        Amount DECIMAL(10,2) NOT NULL,
                        Date DATETIME DEFAULT CURRENT_TIMESTAMP,
                        UserId INTEGER,
                        FOREIGN KEY (UserId) REFERENCES Users(Id)
                    )";

                // جدول المصاريف
                string createExpensesTable = @"
                    CREATE TABLE IF NOT EXISTS Expenses (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Description TEXT NOT NULL,
                        Amount DECIMAL(10,2) NOT NULL,
                        Date DATETIME DEFAULT CURRENT_TIMESTAMP,
                        UserId INTEGER,
                        FOREIGN KEY (UserId) REFERENCES Users(Id)
                    )";

                // جدول حركات الخزنة
                string createVaultMovementsTable = @"
                    CREATE TABLE IF NOT EXISTS VaultMovements (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Type TEXT NOT NULL CHECK(Type IN ('إيداع', 'سحب')),
                        Amount DECIMAL(10,2) NOT NULL,
                        Description TEXT,
                        Date DATETIME DEFAULT CURRENT_TIMESTAMP,
                        UserId INTEGER,
                        FOREIGN KEY (UserId) REFERENCES Users(Id)
                    )";

                // تنفيذ الاستعلامات
                ExecuteNonQuery(connection, createCategoriesTable);
                ExecuteNonQuery(connection, createProductsTable);
                ExecuteNonQuery(connection, createCustomersAndSuppliersTable);
                ExecuteNonQuery(connection, createUsersTable);
                ExecuteNonQuery(connection, createSalesTable);
                ExecuteNonQuery(connection, createSaleDetailsTable);
                ExecuteNonQuery(connection, createPurchasesTable);
                ExecuteNonQuery(connection, createPurchaseDetailsTable);
                ExecuteNonQuery(connection, createIncomesTable);
                ExecuteNonQuery(connection, createExpensesTable);
                ExecuteNonQuery(connection, createVaultMovementsTable);

                // إضافة مستخدم افتراضي
                CreateDefaultUser(connection);
            }
        }

        private static void ExecuteNonQuery(SQLiteConnection connection, string query)
        {
            using (var command = new SQLiteCommand(query, connection))
            {
                command.ExecuteNonQuery();
            }
        }

        private static void CreateDefaultUser(SQLiteConnection connection)
        {
            string checkUserQuery = "SELECT COUNT(*) FROM Users";
            using (var command = new SQLiteCommand(checkUserQuery, connection))
            {
                int userCount = Convert.ToInt32(command.ExecuteScalar());
                if (userCount == 0)
                {
                    string insertUserQuery = @"
                        INSERT INTO Users (Username, Password, FullName, Role) 
                        VALUES ('admin', 'admin123', 'المدير العام', 'مدير')";
                    ExecuteNonQuery(connection, insertUserQuery);
                }
            }
        }
    }
}
