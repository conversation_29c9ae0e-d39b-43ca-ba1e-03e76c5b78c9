using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using EMS.Code.Data;
using EMS.Code.Models;

namespace EMS.Gui.ProductsGui
{
    public partial class ProductsUserControl : UserControl
    {
        private SimpleProductRepository productRepository;

        public ProductsUserControl()
        {
            InitializeComponent();
            productRepository = new SimpleProductRepository();
            LoadProducts();
        }

        private void LoadProducts()
        {
            try
            {
                var products = productRepository.GetAll();
                // يمكن ربط البيانات بـ DataGridView هنا
                // dataGridViewProducts.DataSource = products;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المنتجات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
