﻿namespace EMS
{
    partial class Main
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(Main));
            flowLayoutPanelNavBar = new FlowLayoutPanel();
            buttonHome = new Button();
            buttonProducts = new Button();
            buttonCustomersAndSuppliers = new Button();
            buttonSales = new Button();
            buttonBuys = new Button();
            buttonIncoms = new Button();
            buttonOutcome = new Button();
            buttonStore = new Button();
            buttonReport = new Button();
            buttonUsers = new Button();
            buttonSettings = new Button();
            panelContainer = new Panel();
            flowLayoutPanelNavBar.SuspendLayout();
            SuspendLayout();
            // 
            // flowLayoutPanelNavBar
            // 
            flowLayoutPanelNavBar.AutoScroll = true;
            flowLayoutPanelNavBar.AutoSize = true;
            flowLayoutPanelNavBar.BackColor = Color.FromArgb(64, 64, 64);
            flowLayoutPanelNavBar.Controls.Add(buttonHome);
            flowLayoutPanelNavBar.Controls.Add(buttonProducts);
            flowLayoutPanelNavBar.Controls.Add(buttonCustomersAndSuppliers);
            flowLayoutPanelNavBar.Controls.Add(buttonSales);
            flowLayoutPanelNavBar.Controls.Add(buttonBuys);
            flowLayoutPanelNavBar.Controls.Add(buttonIncoms);
            flowLayoutPanelNavBar.Controls.Add(buttonOutcome);
            flowLayoutPanelNavBar.Controls.Add(buttonStore);
            flowLayoutPanelNavBar.Controls.Add(buttonReport);
            flowLayoutPanelNavBar.Controls.Add(buttonUsers);
            flowLayoutPanelNavBar.Controls.Add(buttonSettings);
            flowLayoutPanelNavBar.Dock = DockStyle.Left;
            flowLayoutPanelNavBar.Location = new Point(0, 0);
            flowLayoutPanelNavBar.Name = "flowLayoutPanelNavBar";
            flowLayoutPanelNavBar.Size = new Size(260, 1016);
            flowLayoutPanelNavBar.TabIndex = 0;
            // 
            // buttonHome
            // 
            buttonHome.AutoSize = true;
            buttonHome.FlatStyle = FlatStyle.Popup;
            buttonHome.ForeColor = Color.White;
            buttonHome.Image = Properties.Resources.home_738873;
            buttonHome.ImageAlign = ContentAlignment.MiddleLeft;
            buttonHome.Location = new Point(5, 5);
            buttonHome.Margin = new Padding(5);
            buttonHome.Name = "buttonHome";
            buttonHome.Size = new Size(250, 65);
            buttonHome.TabIndex = 0;
            buttonHome.Text = "الرئيسية";
            buttonHome.TextImageRelation = TextImageRelation.ImageBeforeText;
            buttonHome.UseVisualStyleBackColor = true;
            // 
            // buttonProducts
            // 
            buttonProducts.AutoSize = true;
            buttonProducts.FlatStyle = FlatStyle.Popup;
            buttonProducts.ForeColor = Color.White;
            buttonProducts.Image = Properties.Resources._12212522;
            buttonProducts.ImageAlign = ContentAlignment.MiddleLeft;
            buttonProducts.Location = new Point(5, 80);
            buttonProducts.Margin = new Padding(5);
            buttonProducts.Name = "buttonProducts";
            buttonProducts.Size = new Size(250, 65);
            buttonProducts.TabIndex = 1;
            buttonProducts.Text = "المنتجات";
            buttonProducts.TextImageRelation = TextImageRelation.ImageBeforeText;
            buttonProducts.UseVisualStyleBackColor = true;
            // 
            // buttonCustomersAndSuppliers
            // 
            buttonCustomersAndSuppliers.AutoSize = true;
            buttonCustomersAndSuppliers.FlatStyle = FlatStyle.Popup;
            buttonCustomersAndSuppliers.ForeColor = Color.White;
            buttonCustomersAndSuppliers.Image = Properties.Resources.business_people_10809501;
            buttonCustomersAndSuppliers.ImageAlign = ContentAlignment.MiddleLeft;
            buttonCustomersAndSuppliers.Location = new Point(5, 155);
            buttonCustomersAndSuppliers.Margin = new Padding(5);
            buttonCustomersAndSuppliers.Name = "buttonCustomersAndSuppliers";
            buttonCustomersAndSuppliers.Size = new Size(250, 65);
            buttonCustomersAndSuppliers.TabIndex = 2;
            buttonCustomersAndSuppliers.Text = "التجار";
            buttonCustomersAndSuppliers.TextImageRelation = TextImageRelation.ImageBeforeText;
            buttonCustomersAndSuppliers.UseVisualStyleBackColor = true;
            // 
            // buttonSales
            // 
            buttonSales.AutoSize = true;
            buttonSales.FlatStyle = FlatStyle.Popup;
            buttonSales.ForeColor = Color.White;
            buttonSales.Image = Properties.Resources.sale_2753001;
            buttonSales.ImageAlign = ContentAlignment.MiddleLeft;
            buttonSales.Location = new Point(5, 230);
            buttonSales.Margin = new Padding(5);
            buttonSales.Name = "buttonSales";
            buttonSales.Size = new Size(250, 65);
            buttonSales.TabIndex = 3;
            buttonSales.Text = "المبيعات";
            buttonSales.TextImageRelation = TextImageRelation.ImageBeforeText;
            buttonSales.UseVisualStyleBackColor = true;
            // 
            // buttonBuys
            // 
            buttonBuys.AutoSize = true;
            buttonBuys.FlatStyle = FlatStyle.Popup;
            buttonBuys.ForeColor = Color.White;
            buttonBuys.Image = Properties.Resources.buy_2752930;
            buttonBuys.ImageAlign = ContentAlignment.MiddleLeft;
            buttonBuys.Location = new Point(5, 305);
            buttonBuys.Margin = new Padding(5);
            buttonBuys.Name = "buttonBuys";
            buttonBuys.Size = new Size(250, 65);
            buttonBuys.TabIndex = 5;
            buttonBuys.Text = "المشتريات";
            buttonBuys.TextImageRelation = TextImageRelation.ImageBeforeText;
            buttonBuys.UseVisualStyleBackColor = true;
            // 
            // buttonIncoms
            // 
            buttonIncoms.AutoSize = true;
            buttonIncoms.FlatStyle = FlatStyle.Popup;
            buttonIncoms.ForeColor = Color.White;
            buttonIncoms.Image = Properties.Resources.cashback_2038772;
            buttonIncoms.ImageAlign = ContentAlignment.MiddleLeft;
            buttonIncoms.Location = new Point(5, 380);
            buttonIncoms.Margin = new Padding(5);
            buttonIncoms.Name = "buttonIncoms";
            buttonIncoms.Size = new Size(250, 65);
            buttonIncoms.TabIndex = 6;
            buttonIncoms.Text = "مداخيل اخرى";
            buttonIncoms.TextImageRelation = TextImageRelation.ImageBeforeText;
            buttonIncoms.UseVisualStyleBackColor = true;
            // 
            // buttonOutcome
            // 
            buttonOutcome.AutoSize = true;
            buttonOutcome.FlatStyle = FlatStyle.Popup;
            buttonOutcome.ForeColor = Color.White;
            buttonOutcome.Image = Properties.Resources.bid_4856853;
            buttonOutcome.ImageAlign = ContentAlignment.MiddleLeft;
            buttonOutcome.Location = new Point(5, 455);
            buttonOutcome.Margin = new Padding(5);
            buttonOutcome.Name = "buttonOutcome";
            buttonOutcome.Size = new Size(250, 65);
            buttonOutcome.TabIndex = 7;
            buttonOutcome.Text = "المصاريف";
            buttonOutcome.TextImageRelation = TextImageRelation.ImageBeforeText;
            buttonOutcome.UseVisualStyleBackColor = true;
            // 
            // buttonStore
            // 
            buttonStore.AutoSize = true;
            buttonStore.FlatStyle = FlatStyle.Popup;
            buttonStore.ForeColor = Color.White;
            buttonStore.Image = Properties.Resources.investment_1809722;
            buttonStore.ImageAlign = ContentAlignment.MiddleLeft;
            buttonStore.Location = new Point(5, 530);
            buttonStore.Margin = new Padding(5);
            buttonStore.Name = "buttonStore";
            buttonStore.Size = new Size(250, 65);
            buttonStore.TabIndex = 8;
            buttonStore.Text = "الخزنة";
            buttonStore.TextImageRelation = TextImageRelation.ImageBeforeText;
            buttonStore.UseVisualStyleBackColor = true;
            // 
            // buttonReport
            // 
            buttonReport.AutoSize = true;
            buttonReport.FlatStyle = FlatStyle.Popup;
            buttonReport.ForeColor = Color.White;
            buttonReport.Image = Properties.Resources.refund_12488796;
            buttonReport.ImageAlign = ContentAlignment.MiddleLeft;
            buttonReport.Location = new Point(5, 605);
            buttonReport.Margin = new Padding(5);
            buttonReport.Name = "buttonReport";
            buttonReport.Size = new Size(250, 65);
            buttonReport.TabIndex = 9;
            buttonReport.Text = "الاحصائيات";
            buttonReport.TextImageRelation = TextImageRelation.ImageBeforeText;
            buttonReport.UseVisualStyleBackColor = true;
            // 
            // buttonUsers
            // 
            buttonUsers.AutoSize = true;
            buttonUsers.FlatStyle = FlatStyle.Popup;
            buttonUsers.ForeColor = Color.White;
            buttonUsers.Image = Properties.Resources.privacy_2133152;
            buttonUsers.ImageAlign = ContentAlignment.MiddleLeft;
            buttonUsers.Location = new Point(5, 680);
            buttonUsers.Margin = new Padding(5);
            buttonUsers.Name = "buttonUsers";
            buttonUsers.Size = new Size(250, 65);
            buttonUsers.TabIndex = 10;
            buttonUsers.Text = "المستخدمين";
            buttonUsers.TextImageRelation = TextImageRelation.ImageBeforeText;
            buttonUsers.UseVisualStyleBackColor = true;
            // 
            // buttonSettings
            // 
            buttonSettings.AutoSize = true;
            buttonSettings.FlatStyle = FlatStyle.Popup;
            buttonSettings.ForeColor = Color.White;
            buttonSettings.Image = Properties.Resources.settings_14954711;
            buttonSettings.ImageAlign = ContentAlignment.MiddleLeft;
            buttonSettings.Location = new Point(5, 755);
            buttonSettings.Margin = new Padding(5);
            buttonSettings.Name = "buttonSettings";
            buttonSettings.Size = new Size(250, 65);
            buttonSettings.TabIndex = 11;
            buttonSettings.Text = "الاعدادات";
            buttonSettings.TextImageRelation = TextImageRelation.ImageBeforeText;
            buttonSettings.UseVisualStyleBackColor = true;
            // 
            // panelContainer
            // 
            panelContainer.BackColor = Color.White;
            panelContainer.Dock = DockStyle.Fill;
            panelContainer.Location = new Point(260, 0);
            panelContainer.Name = "panelContainer";
            panelContainer.Size = new Size(1636, 1016);
            panelContainer.TabIndex = 1;
            // 
            // Main
            // 
            AutoScaleDimensions = new SizeF(168F, 168F);
            AutoScaleMode = AutoScaleMode.Dpi;
            AutoSize = true;
            ClientSize = new Size(1896, 1016);
            Controls.Add(panelContainer);
            Controls.Add(flowLayoutPanelNavBar);
            Font = new Font("Cairo", 12F, FontStyle.Regular, GraphicsUnit.Point, 0);
            Icon = (Icon)resources.GetObject("$this.Icon");
            Margin = new Padding(4, 5, 4, 5);
            Name = "Main";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "EMS";
            WindowState = FormWindowState.Maximized;
            flowLayoutPanelNavBar.ResumeLayout(false);
            flowLayoutPanelNavBar.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private FlowLayoutPanel flowLayoutPanelNavBar;
        private Button buttonHome;
        private Button buttonProducts;
        private Button buttonCustomersAndSuppliers;
        private Button buttonSales;
        private Button buttonBuys;
        private Button buttonIncoms;
        private Button buttonOutcome;
        public Panel panelContainer;
        private Button buttonStore;
        private Button buttonReport;
        private Button buttonUsers;
        private Button buttonSettings;
    }
}
