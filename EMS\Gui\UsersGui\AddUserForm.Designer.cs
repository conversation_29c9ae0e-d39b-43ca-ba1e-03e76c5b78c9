﻿namespace EMS.Gui.UsersGui
{
    partial class AddUserForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            groupBox2 = new GroupBox();
            comboBoxRole = new ComboBox();
            label3 = new Label();
            label4 = new Label();
            buttonSave = new Button();
            label7 = new Label();
            textBoxPassword = new TextBox();
            label8 = new Label();
            label5 = new Label();
            textBoxUserName = new TextBox();
            label6 = new Label();
            groupBox1 = new GroupBox();
            textBoxAddress = new TextBox();
            label2 = new Label();
            textBoxPhone = new TextBox();
            label1 = new Label();
            groupBox3 = new GroupBox();
            flowLayoutPanel1 = new FlowLayoutPanel();
            checkBox2 = new CheckBox();
            checkBox3 = new CheckBox();
            checkBox4 = new CheckBox();
            checkBox5 = new CheckBox();
            checkBox6 = new CheckBox();
            checkBox7 = new CheckBox();
            checkBox8 = new CheckBox();
            checkBox9 = new CheckBox();
            checkBox10 = new CheckBox();
            checkBox11 = new CheckBox();
            checkBox13 = new CheckBox();
            checkBox14 = new CheckBox();
            checkBox15 = new CheckBox();
            checkBox16 = new CheckBox();
            checkBox17 = new CheckBox();
            checkBox18 = new CheckBox();
            checkBox19 = new CheckBox();
            groupBox2.SuspendLayout();
            groupBox1.SuspendLayout();
            groupBox3.SuspendLayout();
            flowLayoutPanel1.SuspendLayout();
            SuspendLayout();
            // 
            // groupBox2
            // 
            groupBox2.AutoSize = true;
            groupBox2.Controls.Add(comboBoxRole);
            groupBox2.Controls.Add(label3);
            groupBox2.Controls.Add(label4);
            groupBox2.Controls.Add(buttonSave);
            groupBox2.Controls.Add(label7);
            groupBox2.Controls.Add(textBoxPassword);
            groupBox2.Controls.Add(label8);
            groupBox2.Controls.Add(label5);
            groupBox2.Controls.Add(textBoxUserName);
            groupBox2.Controls.Add(label6);
            groupBox2.Location = new Point(12, 12);
            groupBox2.Name = "groupBox2";
            groupBox2.Size = new Size(582, 396);
            groupBox2.TabIndex = 2;
            groupBox2.TabStop = false;
            groupBox2.Text = "معلومات الدخول";
            // 
            // comboBoxRole
            // 
            comboBoxRole.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBoxRole.FormattingEnabled = true;
            comboBoxRole.Items.AddRange(new object[] { "مدير", "مستخدم" });
            comboBoxRole.Location = new Point(22, 187);
            comboBoxRole.Name = "comboBoxRole";
            comboBoxRole.Size = new Size(281, 61);
            comboBoxRole.TabIndex = 2;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.ForeColor = Color.Red;
            label3.Location = new Point(322, 197);
            label3.Name = "label3";
            label3.Size = new Size(35, 53);
            label3.TabIndex = 19;
            label3.Text = "*";
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Font = new Font("Cairo", 9.857142F);
            label4.Location = new Point(350, 197);
            label4.Name = "label4";
            label4.Size = new Size(176, 43);
            label4.TabIndex = 18;
            label4.Text = "الصلاحية العامة :";
            // 
            // buttonSave
            // 
            buttonSave.AutoSize = true;
            buttonSave.ForeColor = Color.Black;
            buttonSave.Image = Properties.Resources.diskette_489707;
            buttonSave.ImageAlign = ContentAlignment.MiddleLeft;
            buttonSave.Location = new Point(50, 270);
            buttonSave.Margin = new Padding(5);
            buttonSave.Name = "buttonSave";
            buttonSave.Size = new Size(445, 65);
            buttonSave.TabIndex = 7;
            buttonSave.Text = "حفظ";
            buttonSave.TextImageRelation = TextImageRelation.ImageBeforeText;
            buttonSave.UseVisualStyleBackColor = true;
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.ForeColor = Color.Red;
            label7.Location = new Point(369, 125);
            label7.Name = "label7";
            label7.Size = new Size(35, 53);
            label7.TabIndex = 16;
            label7.Text = "*";
            // 
            // textBoxPassword
            // 
            textBoxPassword.Font = new Font("Cairo", 9.857142F);
            textBoxPassword.Location = new Point(22, 122);
            textBoxPassword.Margin = new Padding(3, 8, 3, 3);
            textBoxPassword.Name = "textBoxPassword";
            textBoxPassword.Size = new Size(281, 51);
            textBoxPassword.TabIndex = 1;
            textBoxPassword.TextAlign = HorizontalAlignment.Center;
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Font = new Font("Cairo", 9.857142F);
            label8.Location = new Point(416, 125);
            label8.Name = "label8";
            label8.Size = new Size(122, 43);
            label8.TabIndex = 14;
            label8.Text = "كلمة السر :";
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.ForeColor = Color.Red;
            label5.Location = new Point(316, 64);
            label5.Name = "label5";
            label5.Size = new Size(35, 53);
            label5.TabIndex = 13;
            label5.Text = "*";
            // 
            // textBoxUserName
            // 
            textBoxUserName.Font = new Font("Cairo", 9.857142F);
            textBoxUserName.Location = new Point(22, 61);
            textBoxUserName.Margin = new Padding(3, 8, 3, 3);
            textBoxUserName.Name = "textBoxUserName";
            textBoxUserName.Size = new Size(281, 51);
            textBoxUserName.TabIndex = 0;
            textBoxUserName.TextAlign = HorizontalAlignment.Center;
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Font = new Font("Cairo", 9.857142F);
            label6.Location = new Point(359, 64);
            label6.Name = "label6";
            label6.Size = new Size(169, 43);
            label6.TabIndex = 11;
            label6.Text = "اسم المستخدم :";
            // 
            // groupBox1
            // 
            groupBox1.AutoSize = true;
            groupBox1.Controls.Add(textBoxAddress);
            groupBox1.Controls.Add(label2);
            groupBox1.Controls.Add(textBoxPhone);
            groupBox1.Controls.Add(label1);
            groupBox1.Location = new Point(600, 12);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new Size(488, 265);
            groupBox1.TabIndex = 3;
            groupBox1.TabStop = false;
            groupBox1.Text = "معلومات اخرى";
            // 
            // textBoxAddress
            // 
            textBoxAddress.Font = new Font("Cairo", 9.857142F);
            textBoxAddress.Location = new Point(16, 155);
            textBoxAddress.Margin = new Padding(3, 8, 3, 3);
            textBoxAddress.Name = "textBoxAddress";
            textBoxAddress.Size = new Size(293, 51);
            textBoxAddress.TabIndex = 6;
            textBoxAddress.TextAlign = HorizontalAlignment.Center;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Font = new Font("Cairo", 9.857142F);
            label2.Location = new Point(338, 158);
            label2.Name = "label2";
            label2.Size = new Size(97, 43);
            label2.TabIndex = 12;
            label2.Text = "العنوان :";
            // 
            // textBoxPhone
            // 
            textBoxPhone.Font = new Font("Cairo", 9.857142F);
            textBoxPhone.Location = new Point(16, 68);
            textBoxPhone.Margin = new Padding(3, 8, 3, 3);
            textBoxPhone.Name = "textBoxPhone";
            textBoxPhone.Size = new Size(293, 51);
            textBoxPhone.TabIndex = 5;
            textBoxPhone.TextAlign = HorizontalAlignment.Center;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Font = new Font("Cairo", 9.857142F);
            label1.Location = new Point(326, 71);
            label1.Name = "label1";
            label1.Size = new Size(133, 43);
            label1.TabIndex = 10;
            label1.Text = "رقم الهاتف :";
            // 
            // groupBox3
            // 
            groupBox3.AutoSize = true;
            groupBox3.Controls.Add(flowLayoutPanel1);
            groupBox3.ForeColor = Color.Red;
            groupBox3.Location = new Point(12, 414);
            groupBox3.Name = "groupBox3";
            groupBox3.Size = new Size(1076, 238);
            groupBox3.TabIndex = 4;
            groupBox3.TabStop = false;
            groupBox3.Text = "الصلاحيات";
            // 
            // flowLayoutPanel1
            // 
            flowLayoutPanel1.Controls.Add(checkBox2);
            flowLayoutPanel1.Controls.Add(checkBox3);
            flowLayoutPanel1.Controls.Add(checkBox4);
            flowLayoutPanel1.Controls.Add(checkBox5);
            flowLayoutPanel1.Controls.Add(checkBox6);
            flowLayoutPanel1.Controls.Add(checkBox7);
            flowLayoutPanel1.Controls.Add(checkBox8);
            flowLayoutPanel1.Controls.Add(checkBox9);
            flowLayoutPanel1.Controls.Add(checkBox10);
            flowLayoutPanel1.Controls.Add(checkBox11);
            flowLayoutPanel1.Controls.Add(checkBox13);
            flowLayoutPanel1.Controls.Add(checkBox14);
            flowLayoutPanel1.Controls.Add(checkBox15);
            flowLayoutPanel1.Controls.Add(checkBox16);
            flowLayoutPanel1.Controls.Add(checkBox17);
            flowLayoutPanel1.Controls.Add(checkBox18);
            flowLayoutPanel1.Controls.Add(checkBox19);
            flowLayoutPanel1.Dock = DockStyle.Fill;
            flowLayoutPanel1.Location = new Point(3, 56);
            flowLayoutPanel1.Name = "flowLayoutPanel1";
            flowLayoutPanel1.Size = new Size(1070, 179);
            flowLayoutPanel1.TabIndex = 0;
            // 
            // checkBox2
            // 
            checkBox2.AutoSize = true;
            checkBox2.Font = new Font("Cairo", 9.857142F, FontStyle.Regular, GraphicsUnit.Point, 0);
            checkBox2.Location = new Point(957, 3);
            checkBox2.Name = "checkBox2";
            checkBox2.Padding = new Padding(3);
            checkBox2.Size = new Size(110, 53);
            checkBox2.TabIndex = 8;
            checkBox2.Text = "اضافة";
            checkBox2.UseVisualStyleBackColor = true;
            // 
            // checkBox3
            // 
            checkBox3.AutoSize = true;
            checkBox3.Font = new Font("Cairo", 9.857142F, FontStyle.Regular, GraphicsUnit.Point, 0);
            checkBox3.Location = new Point(851, 3);
            checkBox3.Name = "checkBox3";
            checkBox3.Padding = new Padding(3);
            checkBox3.Size = new Size(100, 53);
            checkBox3.TabIndex = 9;
            checkBox3.Text = "حذف";
            checkBox3.UseVisualStyleBackColor = true;
            // 
            // checkBox4
            // 
            checkBox4.AutoSize = true;
            checkBox4.Font = new Font("Cairo", 9.857142F, FontStyle.Regular, GraphicsUnit.Point, 0);
            checkBox4.Location = new Point(739, 3);
            checkBox4.Name = "checkBox4";
            checkBox4.Padding = new Padding(3);
            checkBox4.Size = new Size(106, 53);
            checkBox4.TabIndex = 10;
            checkBox4.Text = "تعديل";
            checkBox4.UseVisualStyleBackColor = true;
            // 
            // checkBox5
            // 
            checkBox5.AutoSize = true;
            checkBox5.Font = new Font("Cairo", 9.857142F, FontStyle.Regular, GraphicsUnit.Point, 0);
            checkBox5.Location = new Point(618, 3);
            checkBox5.Name = "checkBox5";
            checkBox5.Padding = new Padding(3);
            checkBox5.Size = new Size(115, 53);
            checkBox5.TabIndex = 11;
            checkBox5.Text = "التصدير";
            checkBox5.UseVisualStyleBackColor = true;
            // 
            // checkBox6
            // 
            checkBox6.AutoSize = true;
            checkBox6.Font = new Font("Cairo", 9.857142F, FontStyle.Regular, GraphicsUnit.Point, 0);
            checkBox6.Location = new Point(491, 3);
            checkBox6.Name = "checkBox6";
            checkBox6.Padding = new Padding(3);
            checkBox6.Size = new Size(121, 53);
            checkBox6.TabIndex = 12;
            checkBox6.Text = "الطباعة";
            checkBox6.UseVisualStyleBackColor = true;
            // 
            // checkBox7
            // 
            checkBox7.AutoSize = true;
            checkBox7.Font = new Font("Cairo", 9.857142F, FontStyle.Regular, GraphicsUnit.Point, 0);
            checkBox7.Location = new Point(192, 3);
            checkBox7.Name = "checkBox7";
            checkBox7.Padding = new Padding(3);
            checkBox7.Size = new Size(293, 53);
            checkBox7.TabIndex = 13;
            checkBox7.Text = "البحث في الصفحة الرئيسية";
            checkBox7.UseVisualStyleBackColor = true;
            // 
            // checkBox8
            // 
            checkBox8.AutoSize = true;
            checkBox8.Font = new Font("Cairo", 9.857142F, FontStyle.Regular, GraphicsUnit.Point, 0);
            checkBox8.Location = new Point(63, 3);
            checkBox8.Name = "checkBox8";
            checkBox8.Padding = new Padding(3);
            checkBox8.Size = new Size(123, 53);
            checkBox8.TabIndex = 14;
            checkBox8.Text = "الرئيسية";
            checkBox8.UseVisualStyleBackColor = true;
            // 
            // checkBox9
            // 
            checkBox9.AutoSize = true;
            checkBox9.Font = new Font("Cairo", 9.857142F, FontStyle.Regular, GraphicsUnit.Point, 0);
            checkBox9.Location = new Point(935, 62);
            checkBox9.Name = "checkBox9";
            checkBox9.Padding = new Padding(3);
            checkBox9.Size = new Size(132, 53);
            checkBox9.TabIndex = 15;
            checkBox9.Text = "المنتجات";
            checkBox9.UseVisualStyleBackColor = true;
            // 
            // checkBox10
            // 
            checkBox10.AutoSize = true;
            checkBox10.Font = new Font("Cairo", 9.857142F, FontStyle.Regular, GraphicsUnit.Point, 0);
            checkBox10.Location = new Point(832, 62);
            checkBox10.Name = "checkBox10";
            checkBox10.Padding = new Padding(3);
            checkBox10.Size = new Size(97, 53);
            checkBox10.TabIndex = 16;
            checkBox10.Text = "التجار";
            checkBox10.UseVisualStyleBackColor = true;
            // 
            // checkBox11
            // 
            checkBox11.AutoSize = true;
            checkBox11.Font = new Font("Cairo", 9.857142F, FontStyle.Regular, GraphicsUnit.Point, 0);
            checkBox11.Location = new Point(693, 62);
            checkBox11.Name = "checkBox11";
            checkBox11.Padding = new Padding(3);
            checkBox11.Size = new Size(133, 53);
            checkBox11.TabIndex = 17;
            checkBox11.Text = "المبيعات";
            checkBox11.UseVisualStyleBackColor = true;
            // 
            // checkBox13
            // 
            checkBox13.AutoSize = true;
            checkBox13.Font = new Font("Cairo", 9.857142F, FontStyle.Regular, GraphicsUnit.Point, 0);
            checkBox13.Location = new Point(541, 62);
            checkBox13.Name = "checkBox13";
            checkBox13.Padding = new Padding(3);
            checkBox13.Size = new Size(146, 53);
            checkBox13.TabIndex = 19;
            checkBox13.Text = "المشتريات";
            checkBox13.UseVisualStyleBackColor = true;
            // 
            // checkBox14
            // 
            checkBox14.AutoSize = true;
            checkBox14.Font = new Font("Cairo", 9.857142F, FontStyle.Regular, GraphicsUnit.Point, 0);
            checkBox14.Location = new Point(363, 62);
            checkBox14.Name = "checkBox14";
            checkBox14.Padding = new Padding(3);
            checkBox14.Size = new Size(172, 53);
            checkBox14.TabIndex = 20;
            checkBox14.Text = "مداخيل اخرى";
            checkBox14.UseVisualStyleBackColor = true;
            // 
            // checkBox15
            // 
            checkBox15.AutoSize = true;
            checkBox15.Font = new Font("Cairo", 9.857142F, FontStyle.Regular, GraphicsUnit.Point, 0);
            checkBox15.Location = new Point(213, 62);
            checkBox15.Name = "checkBox15";
            checkBox15.Padding = new Padding(3);
            checkBox15.Size = new Size(144, 53);
            checkBox15.TabIndex = 21;
            checkBox15.Text = "المصاريف";
            checkBox15.UseVisualStyleBackColor = true;
            // 
            // checkBox16
            // 
            checkBox16.AutoSize = true;
            checkBox16.Font = new Font("Cairo", 9.857142F, FontStyle.Regular, GraphicsUnit.Point, 0);
            checkBox16.Location = new Point(104, 62);
            checkBox16.Name = "checkBox16";
            checkBox16.Padding = new Padding(3);
            checkBox16.Size = new Size(103, 53);
            checkBox16.TabIndex = 22;
            checkBox16.Text = "الخزنة";
            checkBox16.UseVisualStyleBackColor = true;
            // 
            // checkBox17
            // 
            checkBox17.AutoSize = true;
            checkBox17.Font = new Font("Cairo", 9.857142F, FontStyle.Regular, GraphicsUnit.Point, 0);
            checkBox17.Location = new Point(917, 121);
            checkBox17.Name = "checkBox17";
            checkBox17.Padding = new Padding(3);
            checkBox17.Size = new Size(150, 53);
            checkBox17.TabIndex = 23;
            checkBox17.Text = "الاحصائيات";
            checkBox17.UseVisualStyleBackColor = true;
            // 
            // checkBox18
            // 
            checkBox18.AutoSize = true;
            checkBox18.Font = new Font("Cairo", 9.857142F, FontStyle.Regular, GraphicsUnit.Point, 0);
            checkBox18.Location = new Point(741, 121);
            checkBox18.Name = "checkBox18";
            checkBox18.Padding = new Padding(3);
            checkBox18.Size = new Size(170, 53);
            checkBox18.TabIndex = 24;
            checkBox18.Text = "المستخدمين";
            checkBox18.UseVisualStyleBackColor = true;
            // 
            // checkBox19
            // 
            checkBox19.AutoSize = true;
            checkBox19.Font = new Font("Cairo", 9.857142F, FontStyle.Regular, GraphicsUnit.Point, 0);
            checkBox19.Location = new Point(596, 121);
            checkBox19.Name = "checkBox19";
            checkBox19.Padding = new Padding(3);
            checkBox19.Size = new Size(139, 53);
            checkBox19.TabIndex = 25;
            checkBox19.Text = "الاعدادات";
            checkBox19.UseVisualStyleBackColor = true;
            // 
            // AddUserForm
            // 
            AutoScaleDimensions = new SizeF(168F, 168F);
            AutoScaleMode = AutoScaleMode.Dpi;
            AutoSize = true;
            ClientSize = new Size(1106, 661);
            Controls.Add(groupBox3);
            Controls.Add(groupBox1);
            Controls.Add(groupBox2);
            Font = new Font("Cairo", 12F, FontStyle.Regular, GraphicsUnit.Point, 0);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "AddUserForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "اضافة مستخدم";
            groupBox2.ResumeLayout(false);
            groupBox2.PerformLayout();
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            groupBox3.ResumeLayout(false);
            flowLayoutPanel1.ResumeLayout(false);
            flowLayoutPanel1.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private GroupBox groupBox2;
        private Label label7;
        private TextBox textBoxPassword;
        private Label label8;
        private Label label5;
        private TextBox textBoxUserName;
        private Label label6;
        private GroupBox groupBox1;
        private TextBox textBoxPhone;
        private Label label1;
        private TextBox textBoxAddress;
        private Label label2;
        private GroupBox groupBox3;
        private Button buttonSave;
        private FlowLayoutPanel flowLayoutPanel1;
        private CheckBox checkBox2;
        private CheckBox checkBox3;
        private CheckBox checkBox4;
        private CheckBox checkBox5;
        private CheckBox checkBox6;
        private CheckBox checkBox7;
        private CheckBox checkBox8;
        private CheckBox checkBox9;
        private CheckBox checkBox10;
        private CheckBox checkBox11;
        private CheckBox checkBox13;
        private CheckBox checkBox14;
        private CheckBox checkBox15;
        private CheckBox checkBox16;
        private CheckBox checkBox17;
        private CheckBox checkBox18;
        private CheckBox checkBox19;
        private Label label3;
        private Label label4;
        private ComboBox comboBoxRole;
    }
}